# Property Data API

A FastAPI-based service that scrapes and processes property data from Zillow.

## Overview

This API provides endpoints to fetch, process, and return structured property data from Zillow listings. It uses FastAPI for the web framework, Selenium for web scraping, and includes data mapping functionality to transform raw data into a standardized format.

## Features

- Scrape property data from Zillow URLs using Selenium with proxy support
- Transform and map raw data into a structured format
- Multiple parser configurations for different Zillow page types
- RESTful API with JSON responses
- Comprehensive error handling and logging
- CORS support for cross-origin requests

## Installation

### Prerequisites

- Python 3.8+
- pip
- Chrome browser (for Selenium WebDriver)

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```
SCRAPEOPS_API_KEY=your_scrapeops_api_key
```

### Setup

1. Clone the repository:
```bash
git clone <repository-url>
cd <repository-directory>
```

2. Create and activate a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Starting the API Server

```bash
uvicorn main:app --reload
```

The API will be available at http://127.0.0.1:8000.

### API Endpoints

#### POST /api/v1/property

Fetches and maps property data from a Zillow URL.

**Request Body:**
```json
{
  "url": "https://www.zillow.com/apartments/los-angeles-ca/the-james-hollywood/5XjTdw/"
}
```

**Response:**
```json
{
  "data": {
    "address": "123 Main St Los Angeles CA 90001",
    "price": "1500000",
    "bedrooms": "3",
    "bathrooms": "2",
    "county": "Los Angeles",
    "city": "Los Angeles",
    "state": "CA",
    "zipcode": "90001",
    "laundry": "In Unit",
    "heating": "Central",
    "flooring": "Hardwood",
    "parking": "Garage",
    "unitCount": "1",
    "exteriorFeatures": "Patio",
    "interiorFeatures": "Fireplace",
    "agentName": "John Doe",
    "agentPhoneNumber": "************",
    "brokerName": "ABC Realty",
    "brokerPhoneNumber": "************",
    "mlsName": "CRMLS",
    "livingArea": "2000",
    "streetAddress": "123 Main St",
    "description": "Beautiful home in Los Angeles",
    "homeStatus": "FOR_SALE",
    "yearBuilt": "2000",
    "latitude": "34.0522",
    "longitude": "-118.2437",
    "hasCooling": "true",
    "hasHeating": "true",
    "hasGarage": "true",
    "images": ["https://example.com/image1.jpg", "https://example.com/image2.jpg"],
    "floorPlans": [{"bedrooms": "3", "bathrooms": "2", "price": "1500000"}]
  },
  "status": "200"
}
```

#### POST /api/v1/property/regex

Fetches raw HTML data from a Zillow URL.

**Request Body:**
```json
{
  "url": "https://www.zillow.com/apartments/los-angeles-ca/the-james-hollywood/5XjTdw/"
}
```

**Response:**
```json
{
  "data": "HTML content as string",
  "status": "200"
}
```

#### GET /status

Health check endpoint.

**Response:**
```json
{
  "status": "ok"
}
```

### Error Responses

If an error occurs, the API will return:

```json
{
  "data": {},
  "status": "error"
}
```

Or for server errors:

```json
{
  "detail": "Internal Server Error"
}
```

## Project Structure

- `main.py` - FastAPI application and endpoint definitions
- `zillow.py` - Functions for scraping and processing Zillow data
- `mapping.py` - Data transformation and mapping logic
- `scrapper.py` - Selenium-based web scraping functionality
- `utils.py` - Utility functions for data processing
- `logger/` - Logging configuration
- `*.def` files - Parser configuration files for different Zillow page types:
  - `zillowcom_property.def` - Standard property listings
  - `zillowcom_property_3d.def` - 3D tour property listings
  - `zillowcom_property_not.def` - Alternative property page structure
  - `zillowcom_property_rent.def` - Rental property listings
  - `zillowcom_property_rent_house.def` - House rental listings

## Development

### Logging

The application uses a custom logging configuration that writes logs to both the console and a `logs.log` file. The console shows INFO level and above, while the file logs DEBUG level and above.

### Error Handling

The application includes comprehensive error handling to gracefully handle exceptions during scraping and data processing.

### Proxy Support

The scraper uses ScrapeOps proxy service to avoid IP blocking. Make sure to set your SCRAPEOPS_API_KEY in the .env file.

## Documentation

When the server is running, you can access:

- Interactive API documentation: http://127.0.0.1:8000/docs
- Alternative documentation: http://127.0.0.1:8000/redoc

## License

[Your License]
