import logging


def logging_setup(__name__):
    """
    Set up and configure the logger to avoid duplicate log messages.

    Returns:
        logging.Logger: Configured logger instance
    """
    logger = logging.getLogger(__name__)

    # Only configure logger if it hasn't been configured yet
    if not logger.handlers:
        logger.setLevel(logging.DEBUG)
        formatter = logging.Formatter(
            "%(asctime)s - %(levelname)s - %(message)s")

        stream_handler = logging.StreamHandler()
        stream_handler.setLevel(logging.INFO)  # Console shows INFO and above
        stream_handler.setFormatter(formatter)

        file_handler = logging.FileHandler("logs.log")
        file_handler.setLevel(logging.DEBUG)  # File logs everything
        file_handler.setFormatter(formatter)

        logger.addHandler(stream_handler)
        logger.addHandler(file_handler)

    return logger
