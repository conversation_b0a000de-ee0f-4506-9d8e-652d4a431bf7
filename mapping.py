from logger.logger_config import logging_setup
from utils import extract_floor_plan_data, filter_original_photos, find_amenities_by_title, output_formatter


logger = logging_setup(__name__)


def map_data(raw_json, url):
    logger.info("Mapping data function started")
    # Check if raw_json is None
    if raw_json is None:
        logger.error(f"Received None for raw_json from URL: {url}")
        return ""

    # Extract cooling amenities
    cooling_amenities = []
    try:
        if raw_json.get('structuredAmenities', {}).get('unitFeatures', {}).get('amenityGroups'):
            amenity_groups = raw_json.get('structuredAmenities', {}).get(
                'unitFeatures', {}).get('amenityGroups', [])
            cooling_amenities = find_amenities_by_title(
                amenity_groups, "Cooling")
    except Exception as e:
        logger.warning(f"not found cooling amenities: {e}")
        cooling_amenities = []

    # Extract flooring amenities
    logger.info("Extracting flooring amenities")
    flooring_amenities = []
    try:
        if raw_json.get('structuredAmenities', {}).get('unitFeatures', {}).get('amenityGroups'):
            amenity_groups = raw_json.get('structuredAmenities', {}).get(
                'unitFeatures', {}).get('amenityGroups', [])
            flooring_amenities = find_amenities_by_title(
                amenity_groups, "Flooring")
    except Exception as e:
        logger.warning(f" extracting flooring amenities: {e}")
        flooring_amenities = []
    logger.info("Extracting address")
    address = raw_json.get('address', {}).get('streetAddress', '') + ' ' + raw_json.get('address', {}).get('city', '') + \
        ' ' + raw_json.get('address', {}).get('state', '') + \
        ' ' + raw_json.get('address', {}).get('zipcode', '') or "null"
    price = raw_json.get('price') or "null"

    bedrooms = raw_json.get('bedrooms') or raw_json.get('resoFacts', {}).get(
        'bedrooms') or "null"
    bathrooms = raw_json.get('bathrooms') or raw_json.get('resoFacts', {}).get(
        'bathrooms') or "null"
    county = raw_json.get('county') or "null"
    city = raw_json.get('city') or "null"
    state = raw_json.get('state') or raw_json.get(
        'address', {}).get('state') or "null"
    zipcode = raw_json.get('zipcode') or raw_json.get(
        'address', {}).get('zipcode') or "null"
    laundry = raw_json.get('resoFacts', {}).get(
        'laundryFeatures') or raw_json.get('amenitySummary', {}).get('laundry') or "null"
    heating = raw_json.get('resoFacts', {}).get('heating') or raw_json.get(
        'buildingAttributes', {}).get('heatingSource') or "null"
    cooling = raw_json.get('resoFacts', {}).get(
        'cooling') or cooling_amenities or "null"
    flooring = raw_json.get('resoFacts', {}).get(
        'flooring') or flooring_amenities or "null"
    parking = raw_json.get('resoFacts', {}).get('parkingFeatures') or raw_json.get(
        'buildingAttributes', {}).get('parkingTypes') or "null"
    unitCount = raw_json.get('buildingAttributes', {}).get(
        'unitCount') or raw_json.get('resoFacts', {}).get('unitCount') or "null"
    exteriorFeatures = raw_json.get('resoFacts', {}).get(
        'exteriorFeatures') or "null"
    interiorFeatures = raw_json.get('resoFacts', {}).get(
        'interiorFeatures') or "null"
    agentName = raw_json.get('attributionInfo', {}).get(
        'agentName') or raw_json.get('contactInfo', {}).get('agentFullName') or "null"
    agentPhoneNumber = raw_json.get('attributionInfo', {}).get(
        'agentPhoneNumber') or "null"
    brokerName = raw_json.get('attributionInfo', {}).get(
        'brokerName') or "null"
    brokerPhoneNumber = raw_json.get('attributionInfo', {}).get(
        'brokerPhoneNumber') or "null"
    mlsName = raw_json.get('attributionInfo', {}).get('mlsName') or "null"
    livingArea = raw_json.get('resoFacts', {}).get(
        'livingArea') or raw_json.get('livingAreaValue') or "null"
    streetAddress = raw_json.get('streetAddress') or "null"
    description = raw_json.get('description') or "null"
    homeStatus = raw_json.get('homeStatus') or "null"
    yearBuilt = raw_json.get('yearBuilt') or "null"
    latitude = raw_json.get('latitude') or "null"
    longitude = raw_json.get('longitude') or "null"
    hasCooling = raw_json.get('resoFacts', {}).get('hasCooling') or "null"
    hasHeating = raw_json.get('resoFacts', {}).get('hasHeating') or "null"
    hasGarage = raw_json.get('resoFacts', {}).get('hasGarage') or "null"
    bathroomsFull = raw_json.get('resoFacts', {}).get(
        'bathroomsFull') or "null"
    bathroomsHalf = raw_json.get('resoFacts', {}).get(
        'bathroomsHalf') or "null"
    bathroomsThreeQuarter = raw_json.get('resoFacts', {}).get(
        'bathroomsThreeQuarter') or "null"
    lotSize = raw_json.get('lotSize') or "null"
    foundationArea = raw_json.get('resoFacts', {}).get(
        'foundationArea') or "null"
    foundationDetails = raw_json.get('resoFacts', {}).get(
        'foundationDetails') or "null"
    hoaFee = raw_json.get('resoFacts', {}).get('hoaFee') or "null"
    patioAndPorchFeatures = raw_json.get('resoFacts', {}).get(
        'patioAndPorchFeatures') or "null"
    poolFeatures = raw_json.get('resoFacts', {}).get('poolFeatures') or "null"
    waterSource = raw_json.get('resoFacts', {}).get('waterSource') or "null"
    waterView = raw_json.get('resoFacts', {}).get('waterView') or "null"
    elementarySchool = raw_json.get('resoFacts', {}).get(
        'elementarySchool') or "null"
    middleOrJuniorSchool = raw_json.get('resoFacts', {}).get(
        'middleOrJuniorSchool') or "null"
    belowGradeFinishedArea = raw_json.get('resoFacts', {}).get(
        'belowGradeFinishedArea') or "null"
    aboveGradeFinishedArea = raw_json.get('resoFacts', {}).get(
        'aboveGradeFinishedArea') or "null"
    accessibilityFeatures = raw_json.get('resoFacts', {}).get(
        'accessibilityFeatures') or "null"
    fireplaces = raw_json.get('resoFacts', {}).get('fireplaces') or "null"
    Heating = raw_json.get('resoFacts', {}).get('heating') or raw_json.get(
        'buildingAttributes', {}).get('heatingSource') or "null"
    Parking = raw_json.get('resoFacts', {}).get('parking') or raw_json.get(
        'buildingAttributes', {}).get('parkingTypes') or raw_json.get('resoFacts', {}).get('parkingFeatures') or "null"
    Appliances = raw_json.get('resoFacts', {}).get('appliances') or raw_json.get(
        'buildingAttributes', {}).get('appliances') or "null"
    # floorPlans = filter_floor_plan_data(raw_json)
    images = filter_original_photos(raw_json) or "null"
    floorPlans = extract_floor_plan_data(raw_json) or "null"
    source_url = url or "null"

    output = {
        'address': address,
        'price': price,
        'bedrooms': bedrooms,
        'bathrooms': bathrooms,
        'county': county,
        'city': city,
        'state': state,
        'zipcode': zipcode,
        'laundry': laundry,
        'heating': heating,
        'flooring': flooring,
        'parking': parking,
        'unitCount': unitCount,
        'exteriorFeatures': exteriorFeatures,
        'interiorFeatures': interiorFeatures,
        'agentName': agentName,
        'agentPhoneNumber': agentPhoneNumber,
        'brokerName': brokerName,
        'brokerPhoneNumber': brokerPhoneNumber,
        'mlsName': mlsName,
        'livingArea': livingArea,
        'streetAddress': streetAddress,
        'description': description,
        'homeStatus': homeStatus,
        'yearBuilt': yearBuilt,
        'latitude': latitude,
        'longitude': longitude,
        'hasCooling': hasCooling,
        'hasHeating': hasHeating,
        'hasGarage': hasGarage,
        'bathroomsFull': bathroomsFull,
        'bathroomsHalf': bathroomsHalf,
        'bathroomsThreeQuarter': bathroomsThreeQuarter,
        'lotSize': lotSize,
        'foundationArea': foundationArea,
        'foundationDetails': foundationDetails,
        'hoaFee': hoaFee,
        'patioAndPorchFeatures': patioAndPorchFeatures,
        'poolFeatures': poolFeatures,
        'waterSource': waterSource,
        'waterView': waterView,
        'elementarySchool': elementarySchool,
        'middleOrJuniorSchool': middleOrJuniorSchool,
        'belowGradeFinishedArea': belowGradeFinishedArea,
        'aboveGradeFinishedArea': aboveGradeFinishedArea,
        'accessibilityFeatures': accessibilityFeatures,
        'fireplaces': fireplaces,
        'Cooling': cooling,
        'Heating': Heating,
        'Parking': Parking,
        'Appliances': Appliances,
        'floorPlans': floorPlans,
        'images': images,
        'source_url': source_url
    }

    formatted_output = output_formatter(output)
    return formatted_output
