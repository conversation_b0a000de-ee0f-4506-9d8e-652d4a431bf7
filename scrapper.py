# from selenium.webdriver.support import expected_conditions as EC
# from selenium.webdriver.support.ui import WebDriverWait
# from selenium.webdriver.common.by import By
# from seleniumwire import webdriver
# from bs4 import BeautifulSoup
# import os
# from dotenv import load_dotenv
# from logger.logger_config import logging_setup
# import time
# import traceback
# from selenium.webdriver.chrome.options import Options

# load_dotenv()
# SCRAPEOPS_API_KEY = os.getenv("SCRAPEOPS_API_KEY")

# logger = logging_setup(__name__)

# FLOOR_XPATH = """
# //*[
#   (self::button or @role='button' or contains(@class, 'button') or contains(@class, 'btn'))
#   and (
#     contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'floor') or
#     contains(translate(@aria-label, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'floor') or
#     contains(translate(@title, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'floor') or
#     .//span[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'floor')]
#   )
# ]
# """

# def scrape_url(url):
#     chrome_options = Options()
#     chrome_options.add_argument("--headless")
#     chrome_options.add_argument("--disable-gpu")
#     chrome_options.add_argument("--no-sandbox")
#     chrome_options.add_argument("--disable-dev-shm-usage")
#     chrome_options.add_argument("user-agent=Mozilla/5.0")

#     proxy_url = (
#         "https://proxy.scrapeops.io/v1/?"
#         f"api_key={SCRAPEOPS_API_KEY}&"
#         f"url={url}&"
#         "keep_headers=true&"
#         "render_js=true&"
#         "wait=8000"
#     )
#     driver = webdriver.Chrome(options=chrome_options)
#     driver.get(proxy_url)

#     try:
#         WebDriverWait(driver, 20).until(EC.presence_of_element_located((By.TAG_NAME, "body")))
#         logger.info("Initial page loaded")
#         time.sleep(10)  # allow JS rendering
#         source_url = {"source_url": url}
#         full_html = driver.page_source + str(source_url)
#     except Exception as e:
#         logger.error(f"Error waiting for initial page load: {e}")
#         driver.quit()
#         return None

#     try:
#         driver.get(url)
#         WebDriverWait(driver, 15).until(EC.presence_of_element_located((By.TAG_NAME, "body")))

#         # Remove overlays/banners if any
#         try:
#             for sel in ['[id*="banner"]', '[class*="popup"]']:
#                 for el in driver.find_elements(By.CSS_SELECTOR, sel):
#                     driver.execute_script("arguments[0].remove();", el)
#             logger.info("Banners removed")
#         except Exception:
#             logger.warning("No banners found")

#         # Attempt to find and click the floor plan button dynamically
#         try:
#             # Wait a short moment for buttons to appear
#             time.sleep(2)
#             elements = driver.find_elements(By.XPATH, FLOOR_XPATH)
#             logger.info(f"Found {len(elements)} floor-related elements.")

#             for i, el in enumerate(elements):
#                 outer_html = el.get_attribute("outerHTML")
#                 visible = el.is_displayed()
#                 logger.info(f"[{i}] Displayed: {visible} | Outer HTML: {outer_html}")

#             if elements:
#                 button = elements[0]
#                 driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", button)
#                 time.sleep(1)
#                 driver.execute_script("arguments[0].click();", button)
#                 logger.info("Floor-related button clicked via JS.")
#                 time.sleep(5)
#             else:
#                 logger.warning("No floor button found with XPath, trying JS fallback...")

#                 # JS fallback click
#                 js_click = """
#                 let els = Array.from(document.querySelectorAll('button, [role=button], div, span')).filter(el =>
#                   /floor/i.test(el.innerText) || /floor/i.test(el.getAttribute('aria-label') || '') || /floor/i.test(el.getAttribute('title') || '')
#                 );
#                 if (els.length) {
#                   els[0].scrollIntoView({block: 'center'});
#                   els[0].click();
#                   return true;
#                 }
#                 return false;
#                 """
#                 clicked = driver.execute_script(js_click)
#                 if clicked:
#                     logger.info("Floor button clicked via JS fallback.")
#                     time.sleep(5)
#                 else:
#                     logger.warning("Floor button NOT found via JS fallback either.")

#             # Collect floor plan image URLs
#             try:
#                 floor_plan = {}
#                 images_lst = []

#                 WebDriverWait(driver, 15).until(
#                     EC.presence_of_all_elements_located((By.CSS_SELECTOR, "img"))
#                 )

#                 images = driver.find_elements(By.CSS_SELECTOR, "img")
#                 img_urls = set()
#                 for img in images:
#                     src = img.get_attribute("src")
#                     if src and src.startswith("http"):
#                         img_urls.add(src)

#                 images_lst.extend(img_urls)
#                 logger.info(f"Found {len(img_urls)} floor plan image URLs.")
#                 floor_plan['floorPlan'] = images_lst
#                 full_html += str(floor_plan)

#             except Exception as e:
#                 logger.error(f"Floor plan image not found: {e}")

#         except Exception:
#             logger.error("Failed to find or click floor-related button:\n" + traceback.format_exc())

#     except Exception as e:
#         logger.info(f"Error processing URL {url}: {e}")

#     finally:
#         driver.quit()

#     # Clean up HTML
#     soup = BeautifulSoup(full_html, "html.parser")
#     for tag in ["head", "style", "script", "noscript", "iframe", "path", "svg", "button", "footer", "nav"]:
#         for element in soup.find_all(tag):
#             element.decompose()

#     return str(soup)

from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.common.by import By
from seleniumwire import webdriver
from bs4 import BeautifulSoup
import os
from dotenv import load_dotenv
from logger.logger_config import logging_setup
import time
import traceback
from selenium.webdriver.chrome.options import Options
from urllib.parse import urlparse, urlunparse

load_dotenv()
SCRAPEOPS_API_KEY = os.getenv("SCRAPEOPS_API_KEY")
logger = logging_setup(__name__)

FLOOR_XPATH = """
//*[ 
  (self::button or @role='button' or contains(@class, 'button') or contains(@class, 'btn')) 
  and (
    contains(translate(., 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'floor') or 
    contains(translate(@aria-label, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'floor') or 
    contains(translate(@title, 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'floor') or 
    .//span[contains(translate(text(), 'ABCDEFGHIJKLMNOPQRSTUVWXYZ', 'abcdefghijklmnopqrstuvwxyz'), 'floor')]
  )
]
"""


def scrape_url(url):
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("user-agent=Mozilla/5.0")

    proxy_url = (
        "https://proxy.scrapeops.io/v1/?"
        f"api_key={SCRAPEOPS_API_KEY}&"
        f"url={url}&"
        "keep_headers=true&"
        "render_js=true&"
        "wait=8000"
    )

    driver = webdriver.Chrome(options=chrome_options)
    driver.get(proxy_url)

    try:
        WebDriverWait(driver, 20).until(
            EC.presence_of_element_located((By.TAG_NAME, "body")))
        logger.info("Initial page loaded")
        time.sleep(10)  # allow JS rendering
        source_url = {"source_url": url}
        full_html = driver.page_source + str(source_url)
    except Exception as e:
        logger.error(f"Error waiting for initial page load: {e}")
        driver.quit()
        return None

    try:
        driver.get(url)
        WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.TAG_NAME, "body")))

        try:
            for sel in ['[id*="banner"]', '[class*="popup"]']:
                for el in driver.find_elements(By.CSS_SELECTOR, sel):
                    driver.execute_script("arguments[0].remove();", el)
            logger.info("Banners removed")
        except Exception:
            logger.warning("No banners found")

        try:
            time.sleep(2)
            elements = driver.find_elements(By.XPATH, FLOOR_XPATH)
            logger.info(f"Found {len(elements)} floor-related elements.")

            for i, el in enumerate(elements):
                outer_html = el.get_attribute("outerHTML")
                visible = el.is_displayed()
                logger.info(
                    f"[{i}] Displayed: {visible} | Outer HTML: {outer_html}")

            if elements:
                button = elements[0]
                driver.execute_script(
                    "arguments[0].scrollIntoView({block: 'center'});", button)
                time.sleep(1)
                driver.execute_script("arguments[0].click();", button)
                logger.info("Floor-related button clicked via JS.")
                time.sleep(5)
            else:
                logger.warning(
                    "No floor button found with XPath, trying JS fallback...")

                js_click = """
                let els = Array.from(document.querySelectorAll('button, [role=button], div, span')).filter(el =>
                  /floor/i.test(el.innerText) || /floor/i.test(el.getAttribute('aria-label') || '') || /floor/i.test(el.getAttribute('title') || '')
                );
                if (els.length) {
                  els[0].scrollIntoView({block: 'center'});
                  els[0].click();
                  return true;
                }
                return false;
                """
                clicked = driver.execute_script(js_click)
                if clicked:
                    logger.info("Floor button clicked via JS fallback.")
                    time.sleep(5)
                else:
                    logger.warning(
                        "Floor button NOT found via JS fallback either.")

            try:
                floor_plan = {}
                WebDriverWait(driver, 15).until(
                    EC.presence_of_all_elements_located(
                        (By.CSS_SELECTOR, "img"))
                )
                images = driver.find_elements(By.CSS_SELECTOR, "img")

                img_dict = {}
                for img in images:
                    src = img.get_attribute("src")
                    if not src or not src.startswith("http"):
                        continue

                    # Normalize URL by removing query params
                    parsed = urlparse(src)
                    base_url = urlunparse(
                        (parsed.scheme, parsed.netloc, parsed.path, '', '', ''))

                    try:
                        width = int(img.get_attribute("naturalWidth") or 0)
                    except:
                        width = 0

                    if base_url not in img_dict or width > img_dict[base_url]['width']:
                        img_dict[base_url] = {"url": src, "width": width}

                images_lst = [entry["url"] for entry in img_dict.values()]
                logger.info(
                    f"Filtered {len(images_lst)} unique image URLs with highest width.")
                floor_plan['floorPlan'] = images_lst
                full_html += str(floor_plan)

            except Exception as e:
                logger.error(f"Floor plan image not found: {e}")

        except Exception:
            logger.error(
                "Failed to find or click floor-related button:\n" + traceback.format_exc())

    except Exception as e:
        logger.info(f"Error processing URL {url}: {e}")

    finally:
        driver.quit()

    soup = BeautifulSoup(full_html, "html.parser")
    for tag in ["head", "style", "script", "noscript", "iframe", "path", "svg", "button", "footer", "nav"]:
        for element in soup.find_all(tag):
            element.decompose()

    return str(soup)
