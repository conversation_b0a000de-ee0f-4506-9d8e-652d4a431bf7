

from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
import os
from dotenv import load_dotenv
from logger.logger_config import logging_setup
import asyncio
import traceback
from urllib.parse import urlparse, urlunparse

load_dotenv()
SCRAPEOPS_API_KEY = os.getenv("SCRAPEOPS_API_KEY")
logger = logging_setup(__name__)

# CSS selectors for floor plan buttons (more reliable than XPath in Playwright)
FLOOR_SELECTORS = [
    'button:has-text("floor")',
    '[role="button"]:has-text("floor")',
    'button:has-text("Floor")',
    '[role="button"]:has-text("Floor")',
    'button[aria-label*="floor" i]',
    'button[title*="floor" i]',
    '.button:has-text("floor")',
    '.btn:has-text("floor")'
]


async def scrape_url(url):
    """
    Async web scraping using <PERSON><PERSON> with proxy support
    """
    try:
        async with async_playwright() as p:
            # Launch browser with headless mode
            browser = await p.chromium.launch(
                headless=True,
                args=[
                    "--disable-gpu",
                    "--no-sandbox",
                    "--disable-dev-shm-usage"
                ]
            )

            # Create context with user agent
            context = await browser.new_context(
                user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            )

            page = await context.new_page()

            # First, try to load via proxy
            proxy_url = (
                "https://proxy.scrapeops.io/v1/?"
                f"api_key={SCRAPEOPS_API_KEY}&"
                f"url={url}&"
                "keep_headers=true&"
                "render_js=true&"
                "wait=8000"
            )

            try:
                logger.info("Loading page via proxy...")
                await page.goto(proxy_url, wait_until="domcontentloaded", timeout=30000)
                await page.wait_for_selector("body", timeout=20000)
                logger.info("Initial page loaded via proxy")
                await asyncio.sleep(10)  # Allow JS rendering

                source_url = {"source_url": url}
                full_html = await page.content() + str(source_url)

            except Exception as e:
                logger.error(f"Error loading via proxy: {e}")
                await browser.close()
                return None

            # Now load the actual URL
            try:
                logger.info("Loading actual URL...")
                await page.goto(url, wait_until="domcontentloaded", timeout=30000)
                await page.wait_for_selector("body", timeout=15000)

                # Remove overlays/banners
                try:
                    banner_selectors = ['[id*="banner"]', '[class*="popup"]']
                    for selector in banner_selectors:
                        elements = await page.query_selector_all(selector)
                        for element in elements:
                            await element.evaluate("el => el.remove()")
                    logger.info("Banners removed")
                except Exception:
                    logger.warning("No banners found")

                # Try to find and click floor plan button
                try:
                    await asyncio.sleep(2)  # Wait for buttons to appear

                    floor_button = None
                    for selector in FLOOR_SELECTORS:
                        try:
                            floor_button = await page.query_selector(selector)
                            if floor_button:
                                is_visible = await floor_button.is_visible()
                                if is_visible:
                                    logger.info(
                                        f"Found floor button with selector: {selector}")
                                    break
                        except Exception:
                            continue

                    if floor_button:
                        try:
                            await floor_button.scroll_into_view_if_needed()
                            await asyncio.sleep(1)
                            await floor_button.click()
                            logger.info("Floor button clicked successfully")
                            await asyncio.sleep(5)  # Wait for content to load
                        except Exception as e:
                            logger.warning(
                                f"Failed to click floor button: {e}")
                    else:
                        logger.warning(
                            "No floor button found, trying JS fallback...")

                        # JS fallback
                        js_click = """
                        () => {
                            let els = Array.from(document.querySelectorAll('button, [role=button], div, span')).filter(el =>
                              /floor/i.test(el.innerText) || /floor/i.test(el.getAttribute('aria-label') || '') || /floor/i.test(el.getAttribute('title') || '')
                            );
                            if (els.length) {
                              els[0].scrollIntoView({block: 'center'});
                              els[0].click();
                              return true;
                            }
                            return false;
                        }
                        """

                        clicked = await page.evaluate(js_click)
                        if clicked:
                            logger.info("Floor button clicked via JS fallback")
                            await asyncio.sleep(5)
                        else:
                            logger.warning(
                                "Floor button NOT found via JS fallback either")

                    # Collect floor plan images
                    try:
                        floor_plan = {}
                        await page.wait_for_selector("img", timeout=15000)
                        images = await page.query_selector_all("img")

                        img_dict = {}
                        for img in images:
                            src = await img.get_attribute("src")
                            if not src or not src.startswith("http"):
                                continue

                            # Normalize URL by removing query params
                            parsed = urlparse(src)
                            base_url = urlunparse(
                                (parsed.scheme, parsed.netloc, parsed.path, '', '', ''))

                            try:
                                # Get natural width using evaluate
                                width = await img.evaluate("el => el.naturalWidth") or 0
                            except:
                                width = 0

                            if base_url not in img_dict or width > img_dict[base_url]['width']:
                                img_dict[base_url] = {
                                    "url": src, "width": width}

                        images_lst = [entry["url"]
                                      for entry in img_dict.values()]
                        logger.info(
                            f"Filtered {len(images_lst)} unique image URLs with highest width")
                        floor_plan['floorPlan'] = images_lst
                        full_html += str(floor_plan)

                    except Exception as e:
                        logger.error(
                            f"Floor plan image collection failed: {e}")

                except Exception:
                    logger.error(
                        "Failed to find or click floor-related button:\n" + traceback.format_exc())

            except Exception as e:
                logger.error(f"Error processing URL {url}: {e}")

            finally:
                await browser.close()

            # Clean up HTML
            soup = BeautifulSoup(full_html, "html.parser")
            for tag in ["head", "style", "script", "noscript", "iframe", "path", "svg", "button", "footer", "nav"]:
                for element in soup.find_all(tag):
                    element.decompose()

            return str(soup)

    except Exception as e:
        logger.error(f"Critical error in scrape_url: {e}")
        return None
