from logger.logger_config import logging_setup

logger = logging_setup(__name__)


def extract_floor_plan_data(data):
    try:
        logger.info("extracting floor plan data")
        floor_plans = []
        floors = data.get("floorPlans")

        for floor in floors:

            floor_dict = {}
            units = floor.get("units")
            if units:

                for unit in units:
                    floor_dict['unitnumber'] = unit.get("unitNumber")
                    floor_dict['price'] = unit.get("price")
                    floor_dict['beds'] = unit.get("beds")
                    floor_dict['baths'] = unit.get("baths")
            else:
                floor_dict['unitnumber'] = "null"
                floor_dict['price'] = floor.get("minPrice")
                floor_dict['beds'] = floor.get("beds")
                floor_dict['baths'] = floor.get("baths")
            try:
                floor_dict['bluePrint'] = floor.get("photos")[1].get("url")
            except Exception as e:
                logger.info(f" No photos found")
                floor_dict['bluePrint'] = "null"
            floor_plans.append(floor_dict)
        logger.info(f"Total extracted floor plan {len(floor_plans)}")
        return floor_plans
    except Exception as e:
        logger.info(f" No floor plan data found: {e}")
        return []


def filter_original_photos(raw_json):
    """
    Filter originalPhotos to only keep jpeg URLs with width=1024.
    """
    try:
        logger.info("Filtering original photos")
        urls = []
        photos = []
        if 'galleryPhotos' in raw_json:
            photos = raw_json.get('galleryPhotos', [])
        elif 'originalPhotos' in raw_json:
            photos = raw_json.get('originalPhotos', [])
        elif 'photos' in raw_json:
            photos = raw_json.get('photos', [])
        for photo in photos:
            mixed_sources = photo.get('mixedSources', {})
            # Filter jpeg sources to only keep width=1024
            if 'jpeg' in mixed_sources:
                jpeg_sources = mixed_sources.get('jpeg', [])
                filtered_jpeg = [
                    source for source in jpeg_sources if source.get('width') == 1024 or source.get('width') == 960]
                # Extract just the URLs
                for source in filtered_jpeg:
                    if 'url' in source:
                        urls.append(source['url'])
        logger.info(
            f"Successfully filtered original photos for URL: {len(urls)}")
        return urls
    except Exception as e:
        logger.error(f"Error filtering original photos: {e}")
        return []


# function to find amenities by title

def find_amenities_by_title(amenity_groups, title):
    logger.info(f"Finding amenities by title: {title}")
    for group in amenity_groups:
        if group.get('title') == title:
            return group.get('amenities', [])
    return []


def output_formatter(data):
    """
    If input is a dictionary, convert list values to comma-separated strings.
    If input is a list, return a comma-separated string.
    Otherwise, return the value as-is.
    """
    if isinstance(data, dict):
        return {
            key: ', '.join(str(v)
                           for v in value) if isinstance(value, list) else value
            for key, value in data.items()
        }
    elif isinstance(data, list):
        return ', '.join(str(v) for v in data)
    else:
        return data
