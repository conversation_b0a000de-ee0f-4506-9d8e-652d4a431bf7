from urllib.parse import urlencode
import aiohttp
import asyncio
import configparser
import json
from bs4 import BeautifulSoup
import html
import os
import sys
from logger.logger_config import logging_setup
from dotenv import load_dotenv
import pandas as pd

load_dotenv()

sys.path.append(os.path.abspath(
    os.path.join(os.path.dirname(__file__), "../")))

logger = logging_setup(__name__)


# Cleaned headers
headers = {
    "accept": "*/*",
    "accept-encoding": "gzip, deflate, br, zstd",
    "accept-language": "en-US,en;q=0.9",
    "origin": "https://www.zillow.com",
    "referer": "https://www.zillow.com/ca/",
    "sec-ch-ua": '"Chromium";v="128", "Not;A=Brand";v="24", "Google Chrome";v="128"',
    "sec-ch-ua-mobile": "?1",
    "sec-ch-ua-platform": "Android",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
}


SCRAPEOPS_API_KEY = os.getenv("SCRAPEOPS_API_KEY")


async def process_url(url, tag, attribute_id, json_path, include_keys, exclude_keys):
    try:
        json_data = await extract_json_from_url(url, tag, attribute_id)
        processed_data = process_json_data(
            json_data, json_path, include_keys, exclude_keys
        )

        if isinstance(processed_data, (dict, list)):
            document = {
                "data": processed_data,
            }
            return document
        else:
            logger.error(f"Processed data is not a dictionary or list")

    except Exception as e:
        logger.error(f"[ERROR] error processing URL {url}: with this key")


def load_parser_definition(file_path):
    config = configparser.ConfigParser()
    config.read(file_path)
    parsing_method = config["Parsing Method"]["method"]
    tag = config["Selectors"]["tag"]
    attribute_id = config["Selectors"]["attribute_id"]
    json_path = config["Processing Instructions"]["json_path"]

    if "Include Keys" in config:
        include_keys = config["Include Keys"].get("keys", "").split(",")
        if include_keys == [""]:
            include_keys = None
    else:
        include_keys = None

    if "Exclude Keys" in config:
        exclude_keys = config["Exclude Keys"].get("keys", "").split(",")
        if exclude_keys == [""]:
            exclude_keys = []
    else:
        exclude_keys = []

    return parsing_method, tag, attribute_id, json_path, include_keys, exclude_keys


async def extract_json_from_url(url, tag, attribute_id):
    try:
        headers = {
            "Content-Type": "application/json",
            "accept": "*/*",
            "accept-encoding": "gzip, deflate, br, zstd",
            "accept-language": "en-US,en;q=0.9",
            "origin": "https://www.zillow.com",
            "referer": "https://www.zillow.com/ca/",
            "sec-ch-ua": '"Chromium";v="128", "Not;A=Brand";v="24", "Google Chrome";v="128"',
            "sec-ch-ua-mobile": "?1",
            "sec-ch-ua-platform": '"Android"',
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "cors",
            "sec-fetch-site": "same-origin",
            "user-agent": "Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
        }
        proxy_params = {
            "api_key": SCRAPEOPS_API_KEY,
            "url": f"{url}",
            "keep_headers": True,
            "render_js": True,  # Enable JS rendering which is likely needed for __NEXT_DATA__
        }

        logger.info(f"Making request to {url}")

        # Create timeout for aiohttp
        timeout = aiohttp.ClientTimeout(total=120)

        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.get(
                url="https://proxy.scrapeops.io/v1/",
                headers=headers,
                params=proxy_params,
            ) as response:
                response.raise_for_status()
                logger.info(
                    f"Response received with status code: {response.status}")

                content = await response.read()
                soup = BeautifulSoup(content, "html.parser")

                # Look for the script tag
                script_tag = soup.find(tag, id=attribute_id)

                if script_tag:
                    logger.info(f"Found script tag with id: {attribute_id}")

                    # Try to get content safely, avoiding attribute access that pylance doesn't like
                    try:
                        # Use get_text() as the primary method
                        content = script_tag.get_text().strip()
                        logger.info("Extracted content using get_text()")

                        if content:
                            script_content = html.unescape(content)
                            json_data = json.loads(script_content)
                            logger.info("Successfully parsed JSON data")
                            return json_data
                        else:
                            # If get_text() returns empty, try direct string representation
                            content = str(script_tag).strip()
                            # Try to extract just the content between tags if it's an HTML tag
                            if content.startswith("<") and content.endswith(">"):
                                inner_content = content[
                                    content.find(">") + 1: content.rfind("<")
                                ]
                                if inner_content:
                                    content = inner_content
                                    logger.info(
                                        "Extracted content from string representation")

                            script_content = html.unescape(content)
                            json_data = json.loads(script_content)
                            logger.info(
                                "Successfully parsed JSON data from alternative method")
                            return json_data
                    except Exception as e:
                        logger.error(
                            f"Error extracting or parsing content: {e}")
                        raise
                else:
                    # Try to find all script tags to help with debugging
                    all_scripts = soup.find_all("script")
                    # Create a list of script IDs safely
                    script_ids = []
                    for s in all_scripts:
                        try:
                            # Try direct string representation to see what's there
                            script_str = str(s)
                            if "id=" in script_str:
                                script_ids.append(f"Has ID attribute in HTML")
                            else:
                                script_ids.append("No ID in HTML")
                        except:
                            script_ids.append("Error getting script info")

                    logger.error(
                        f"Script tag with id '{attribute_id}' not found. Found {len(all_scripts)} script tags."
                    )
                    raise ValueError(
                        f"No script tag with id '{attribute_id}' found")
    except Exception as e:
        logger.error(f"Error fetching the URL: {e}")
        raise


def process_json_data(data, json_path, include_keys, exclude_keys):
    try:
        keys = json_path.split(".")
        for key in keys:
            if key not in data:
                possible_key = next(
                    (k for k in data.keys() if k.startswith(key)), None)
                if possible_key:
                    data = data[possible_key]
                else:
                    logger.error(f"Current level keys: {data.keys()}")
                    raise KeyError(
                        f"Key '{key}' not found in the JSON data at current level."
                    )
            else:
                data = data[key]

            if isinstance(data, str):
                try:
                    data = json.loads(html.unescape(data))
                except json.JSONDecodeError:
                    logger.warning(
                        f"Value for key '{key}' is not valid JSON, proceeding with raw string value."
                    )
                    break

        # Only apply exclude_keys if data is a dictionary
        if isinstance(data, dict) and exclude_keys:
            for key in exclude_keys:
                if key in data:
                    del data[key]

        if include_keys is not None and isinstance(data, dict):
            filtered_data = {k: v for k,
                             v in data.items() if k in include_keys}
        else:
            filtered_data = data

        # Double check type before operating on it
        if isinstance(filtered_data, dict) and exclude_keys:
            for key in exclude_keys:
                if key in filtered_data:
                    del filtered_data[key]

        return filtered_data
    except (AttributeError, KeyError) as e:
        logger.error(f"error: {e}")
        raise


async def get_required_fields(url):
    script_dir = os.path.dirname(os.path.abspath(__file__))
    parser_file_path = os.path.join(script_dir, "zillowcom_property.def")
    parser_file_path_3d = os.path.join(script_dir, "zillowcom_property_3d.def")
    parser_file_path_not = os.path.join(
        script_dir, "zillowcom_property_not.def")
    parser_file_path_rent = os.path.join(
        script_dir, "zillowcom_property_rent.def")
    parser_file_path_rent_house = os.path.join(
        script_dir, "zillowcom_property_rent_house.def")
    parser_file_path_3d2 = os.path.join(
        script_dir, "zillowcom_property_3d2.def")
    parser_files = [parser_file_path_rent_house, parser_file_path,
                    parser_file_path_not, parser_file_path_3d, parser_file_path_rent, parser_file_path_3d2]

    logger.info(f"Processing URL: {url}")

    for parser_file_path in parser_files:
        try:
            if not os.path.exists(parser_file_path):
                logger.error(f"Parser file does not exist: {parser_file_path}")
                continue

            logger.info(f"Trying parser: {parser_file_path}")
            parsing_method, tag, attribute_id, json_path, include_keys, exclude_keys = (
                load_parser_definition(parser_file_path)
            )

            logger.info(
                f"Extracted parser config - tag: {tag}, attribute_id: {attribute_id}, json_path: {json_path}"
            )

            dcs = await process_url(
                url, tag, attribute_id, json_path, include_keys, exclude_keys
            )

            if dcs and "data" in dcs:
                data = dcs["data"]
                logger.info(
                    f"Successfully extracted data with parser: {parser_file_path}"
                )
                return data
            else:
                logger.warning(f"Parser {parser_file_path} found no data")

        except Exception as e:
            logger.error(
                f"Error encountered with file {parser_file_path} and url: {url}. Error: {str(e)}"
            )
            logger.error(
                f"Error encountered with file {parser_file_path} and url: {url}. Error: {str(e)}"
            )
            # Print full exception for debugging
            import traceback

            logger.error(f"Full exception: {traceback.format_exc()}")

    # If none of the parsers succeeded
    logger.error(f"All parsers failed for URL: {url}")
    return None
