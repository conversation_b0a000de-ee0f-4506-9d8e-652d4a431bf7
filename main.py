
from fastapi.middleware.cors import CORSMiddleware
from zillow import get_required_fields
from mapping import map_data
from logger.logger_config import logging_setup
from typing import Dict, Any, Optional, Union, List
import uvicorn
from pydantic import BaseModel, Field
from fastapi import FastAPI, HTTPException, status
import json
from scrapper import scrape_url


logger = logging_setup(__name__)

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    # Or use specific origins like ["http://localhost:3000"]
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


class PropertyRequest(BaseModel):
    url: str


class GeneralPropertyResponse(BaseModel):
    data: str
    status: str


class PropertyResponse(BaseModel):
    data: Union[Dict[str, Any], List[Any], str]
    status: str


@app.post("/api/v1/property/regex", response_model=GeneralPropertyResponse)
async def get_general_property_data(request: PropertyRequest):
    """
    Scrape property data from Zillow

    Args:
        request: PropertyRequest with URL to scrape

    Returns:
        PropertyResponse with scraped data
    """
    try:
        # Quick validation of URL format
        if not request.url.startswith("http"):
            return GeneralPropertyResponse(
                data="",
                status="error",
            )

        # Call the scraper function

        html = scrape_url(request.url)
        if html:
            return GeneralPropertyResponse(data=str(html), status="200")

        else:
            return GeneralPropertyResponse(
                data="", status="404"
            )
    except Exception as e:
        logger.error(f"Error processing URL {request.url}: {str(e)}")
        raise HTTPException(
            status_code=500,)


@app.post("/api/v1/property", response_model=PropertyResponse)
async def get_property_data(request: PropertyRequest):
    """
    Scrape property data from Zillow

    Args:
        request: PropertyRequest with URL to scrape

    Returns:
        PropertyResponse with scraped data
    """
    try:
        # Quick validation of URL format
        if not request.url.startswith("http"):
            return PropertyResponse(
                data={},
                status="error",
            )

        # Call the scraper function
        raw_json = get_required_fields(request.url)
        if raw_json:
            with open("raw_json.json", "w") as f:
                json.dump(raw_json, f)
        mapped_data = map_data(raw_json, request.url)
        if mapped_data:
            return PropertyResponse(data=mapped_data, status="200")
        else:
            return PropertyResponse(
                data={}, status="404"
            )
    except Exception as e:
        logger.error(f"Error processing URL {request.url}: {str(e)}")
        raise HTTPException(
            status_code=500,)


@app.get("/status", status_code=status.HTTP_200_OK)
async def health_check():
    """
    Simple health check endpoint to verify the API is running
    """
    return {"status": "ok"}


if __name__ == "__main__":
    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)
